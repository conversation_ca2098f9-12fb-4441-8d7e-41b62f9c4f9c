pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // Add JetBrains repository for Kotlin artifacts
        maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
        // Add additional Google repository endpoints
        maven("https://dl.google.com/dl/android/maven2/")
    }
}

rootProject.name = "Ariel"
include(":app")
